<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATS Dashboard - Loading...</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 40px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 4px; 
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb; 
        }
        .warning { 
            background: #fff3cd; 
            color: #856404; 
            border: 1px solid #ffeaa7; 
        }
        a { 
            color: #007bff; 
            text-decoration: none; 
        }
        a:hover { 
            text-decoration: underline; 
        }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            border-radius: 4px; 
            margin: 10px 5px; 
            text-decoration: none;
        }
        .btn:hover { 
            background: #0056b3; 
            color: white; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ATS Dashboard</h1>
        <p>The ATS Dashboard is starting up...</p>
        
        <div class="status success">
            <strong>✅ Frontend Container:</strong> Running successfully on port 80
        </div>
        
        <div class="status success">
            <strong>✅ API Server:</strong> Running successfully on port 3001
        </div>
        
        <div class="status info">
            <strong>ℹ️ Status:</strong> The React application is being built. This page will be replaced with the full dashboard once the build completes.
        </div>
        
        <div class="status warning">
            <strong>⚠️ Next Steps:</strong> The React frontend needs to be built and deployed. The API is fully functional.
        </div>
        
        <h3>Quick Links:</h3>
        <a href="http://localhost:3001/health" class="btn" target="_blank">Check API Health</a>
        <a href="http://localhost:3001/api" class="btn" target="_blank">API Endpoints</a>
        
        <h3>Services Status:</h3>
        <ul>
            <li><strong>Frontend (Nginx):</strong> http://localhost (this page)</li>
            <li><strong>API Server:</strong> http://localhost:3001</li>
            <li><strong>PostgreSQL:</strong> Internal (postgres:5432)</li>
            <li><strong>Redis:</strong> Internal (redis:6379)</li>
            <li><strong>MinIO:</strong> Internal (minio:9000)</li>
            <li><strong>Typesense:</strong> http://localhost:8108</li>
        </ul>
        
        <p><em>All Docker containers are running successfully!</em></p>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds to check if the React app is ready
        setTimeout(() => {
            fetch('/version.json')
                .then(response => response.json())
                .then(data => {
                    if (data && data.version) {
                        location.reload();
                    }
                })
                .catch(() => {
                    // React app not ready yet, continue showing this page
                });
        }, 30000);
    </script>
</body>
</html>
