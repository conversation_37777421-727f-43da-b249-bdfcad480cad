import { useState, useEffect, useCallback } from 'react';
import { clientsApi } from '@/services/apiService';
import { useToast } from '@/hooks/use-toast';
import { Client } from '@/types/client';

export function useClients() {
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchClients = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await clientsApi.getAll();
      setClients(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch clients'));
      toast({
        title: "Error",
        description: "Failed to fetch clients. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  const createClient = async (clientData: Omit<Client, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newClient = await clientsApi.create(clientData);
      setClients(prev => [...prev, newClient]);
      toast({
        title: "Success",
        description: "Client created successfully.",
      });
      return newClient;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create client';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const updateClient = async (id: string, clientData: Partial<Client>) => {
    try {
      const updatedClient = await clientsApi.update(id, clientData);
      setClients(prev =>
        prev.map(client => client.id === id ? updatedClient : client)
      );
      toast({
        title: "Success",
        description: "Client updated successfully.",
      });
      return updatedClient;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update client';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const deleteClient = async (id: string) => {
    try {
      await clientsApi.delete(id);
      setClients(prev => prev.filter(client => client.id !== id));
      toast({
        title: "Success",
        description: "Client deleted successfully.",
      });
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete client';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  return {
    clients,
    isLoading,
    error,
    refresh: fetchClients,
    createClient,
    updateClient,
    deleteClient
  };
}

export function useClient(id: string) {
  const [client, setClient] = useState<Client | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchClient = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await clientsApi.getById(id);
      setClient(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(`Failed to fetch client with ID ${id}`));
      toast({
        title: "Error",
        description: "Failed to fetch client details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    fetchClient();
  }, [fetchClient]);

  return {
    client,
    isLoading,
    error,
    refresh: fetchClient
  };
}
export function useDeleteClient() {
  const { toast } = useToast();

  const deleteClient = async (id: string) => {
    try {
      await clientsApi.delete(id);
      toast({
        title: "Success",
        description: "Client deleted successfully.",
      });
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete client';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  return { deleteClient };
}

export function useCreateClient() {
  const { toast } = useToast();

  const createClient = async (clientData: Omit<Client, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newClient = await clientsApi.create(clientData);
      toast({
        title: "Success",
        description: "Client created successfully.",
      });
      return newClient;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create client';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  return { createClient };
}

export function useUpdateClient() {
  const { toast } = useToast();

  const updateClient = async (id: string, clientData: Partial<Client>) => {
    try {
      const updatedClient = await clientsApi.update(id, clientData);
      toast({
        title: "Success",
        description: "Client updated successfully.",
      });
      return updatedClient;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update client';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  return { updateClient };
}

export default useClients;
