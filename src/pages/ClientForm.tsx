import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import ClientFormComponent from '@/components/clients/ClientForm';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useClient } from '@/hooks/useClients';
import { useToast } from '@/hooks/use-toast';
import { clientsApi } from '@/services/apiService';

export default function ClientFormPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const isEditMode = Boolean(id);

  // Obtener datos del cliente si estamos en modo edición
  const { client, isLoading: isLoadingClient } = useClient(id || '');

  // Estado para el guardado
  const [isSaving, setIsSaving] = useState(false);

  // Log para verificar el modo y los datos
  if (import.meta.env.DEV) {
    console.log('Client form:', { isEditMode, client, isLoadingClient });
  }

  // Manejar el envío del formulario
  const handleSubmit = async (data: any) => {
    setIsSaving(true);
    try {
      if (isEditMode && id) {
        // Actualizar cliente existente
        await clientsApi.update(id, data);
        toast({
          title: 'Success',
          description: 'Client updated successfully',
        });
      } else {
        // Crear nuevo cliente
        await clientsApi.create(data);
        toast({
          title: 'Success',
          description: 'Client created successfully',
        });
      }

      // Redirigir a la lista de clientes
      navigate('/clients');
    } catch (error) {
      console.error('Error saving client:', error);
      toast({
        title: 'Error',
        description: 'Failed to save client. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditMode ? 'Edit Client' : 'Add New Client'}
        </h1>
      </div>

      <ClientFormComponent
        client={client}
        onSubmit={handleSubmit}
        isLoading={isLoadingClient || isSaving || createClientMutation.isPending || updateClientMutation.isPending}
      />
    </DashboardLayout>
  );
}
